import { z } from "zod";

// Base schemas for common fields
const mongoIdSchema = z.string().min(1, "ID is required");
const userIdSchema = z.string().min(1, "User ID is required");

// Category schemas
export const createCategorySchema = z.object({
  name: z.string().min(1, "Category name is required").max(50, "Category name too long"),
  type: z.enum(["income", "expense"], {
    errorMap: () => ({ message: "Type must be either 'income' or 'expense'" })
  }),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid color format").optional(),
  icon: z.string().min(1, "Icon is required").optional(),
});

export const updateCategorySchema = createCategorySchema.partial();

export const categoryIdSchema = z.object({
  id: mongoIdSchema,
});

// Transaction schemas
export const createTransactionSchema = z.object({
  amount: z.number().positive("Amount must be greater than 0").max(999999999, "Amount too large"),
  type: z.enum(["income", "expense"], {
    errorMap: () => ({ message: "Type must be either 'income' or 'expense'" })
  }),
  description: z.string().min(1, "Description is required").max(200, "Description too long"),
  categoryId: mongoIdSchema,
  date: z.date().optional(),
  tags: z.array(z.string().max(30, "Tag too long")).max(10, "Too many tags").optional(),
  recurring: z.object({
    enabled: z.boolean().optional(),
    frequency: z.enum(["daily", "weekly", "monthly", "yearly"]).optional(),
    nextDate: z.date().optional(),
  }).optional(),
});

export const updateTransactionSchema = createTransactionSchema.partial();

export const transactionIdSchema = z.object({
  id: mongoIdSchema,
});

export const getTransactionsSchema = z.object({
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(20),
  type: z.enum(["income", "expense"]).optional(),
  categoryId: mongoIdSchema.optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  search: z.string().max(100).optional(),
});

// Budget schemas
export const createBudgetSchema = z.object({
  name: z.string().min(1, "Budget name is required").max(100, "Budget name too long"),
  amount: z.number().positive("Budget amount must be greater than 0").max(999999999, "Amount too large"),
  period: z.enum(["weekly", "monthly", "quarterly", "yearly"], {
    errorMap: () => ({ message: "Period must be weekly, monthly, quarterly, or yearly" })
  }),
  categoryIds: z.array(mongoIdSchema).min(1, "At least one category is required"),
  startDate: z.date(),
  endDate: z.date(),
  alertThreshold: z.number().min(0, "Alert threshold cannot be negative").max(100, "Alert threshold cannot exceed 100%").optional(),
}).refine(
  (data) => data.endDate > data.startDate,
  {
    message: "End date must be after start date",
    path: ["endDate"],
  }
);

export const updateBudgetSchema = createBudgetSchema.partial();

export const budgetIdSchema = z.object({
  id: mongoIdSchema,
});

// Analytics schemas
export const getAnalyticsSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
  groupBy: z.enum(["day", "week", "month", "year"]).optional().default("month"),
}).refine(
  (data) => data.endDate > data.startDate,
  {
    message: "End date must be after start date",
    path: ["endDate"],
  }
);

// Export type definitions for use in frontend
export type CreateCategoryInput = z.infer<typeof createCategorySchema>;
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>;
export type CreateTransactionInput = z.infer<typeof createTransactionSchema>;
export type UpdateTransactionInput = z.infer<typeof updateTransactionSchema>;
export type GetTransactionsInput = z.infer<typeof getTransactionsSchema>;
export type CreateBudgetInput = z.infer<typeof createBudgetSchema>;
export type UpdateBudgetInput = z.infer<typeof updateBudgetSchema>;
export type GetAnalyticsInput = z.infer<typeof getAnalyticsSchema>;
