import { TRPCError } from "@trpc/server";
import { Transaction, Category } from "../db/models/financial.model";
import { protectedProcedure, router } from "../lib/trpc";
import {
  createTransactionSchema,
  updateTransactionSchema,
  transactionIdSchema,
  getTransactionsSchema,
} from "../lib/schemas";

export const transactionsRouter = router({
  // Get transactions with pagination and filtering
  getAll: protectedProcedure
    .input(getTransactionsSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { page, limit, type, categoryId, startDate, endDate, search } =
          input;
        const skip = (page - 1) * limit;

        // Build filter query
        const filter: any = { userId: ctx.session.user.id };

        if (type) filter.type = type;
        if (categoryId) filter.categoryId = categoryId;
        if (startDate || endDate) {
          filter.date = {};
          if (startDate) filter.date.$gte = startDate;
          if (endDate) filter.date.$lte = endDate;
        }
        if (search) {
          filter.$or = [
            { description: { $regex: search, $options: "i" } },
            { tags: { $in: [new RegExp(search, "i")] } },
          ];
        }

        // Get transactions with category details
        const transactions = await Transaction.find(filter)
          .populate("categoryId", "name type color icon")
          .sort({ date: -1, createdAt: -1 })
          .skip(skip)
          .limit(limit);

        const total = await Transaction.countDocuments(filter);

        return {
          transactions,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transactions",
        });
      }
    }),

  // Create a new transaction
  create: protectedProcedure
    .input(createTransactionSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Verify category exists and belongs to user
        const category = await Category.findOne({
          _id: input.categoryId,
          $or: [{ userId: ctx.session.user.id }, { isDefault: true }],
        });

        if (!category) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Category not found",
          });
        }

        // Ensure transaction type matches category type
        if (input.type !== category.type) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Transaction type must match category type",
          });
        }

        const transaction = new Transaction({
          ...input,
          userId: ctx.session.user.id,
          date: input.date || new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        await transaction.save();

        // Populate category details for response
        await transaction.populate("categoryId", "name type color icon");

        return transaction;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create transaction",
        });
      }
    }),

  // Update an existing transaction
  update: protectedProcedure
    .input(transactionIdSchema.merge(updateTransactionSchema))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...updateData } = input;

        const transaction = await Transaction.findOne({
          _id: id,
          userId: ctx.session.user.id,
        });

        if (!transaction) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Transaction not found",
          });
        }

        // If updating category, verify it exists and type matches
        if (updateData.categoryId) {
          const category = await Category.findOne({
            _id: updateData.categoryId,
            $or: [{ userId: ctx.session.user.id }, { isDefault: true }],
          });

          if (!category) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Category not found",
            });
          }

          const transactionType = updateData.type || transaction.type;
          if (transactionType !== category.type) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Transaction type must match category type",
            });
          }
        }

        Object.assign(transaction, updateData);
        transaction.updatedAt = new Date();

        await transaction.save();
        await transaction.populate("categoryId", "name type color icon");

        return transaction;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update transaction",
        });
      }
    }),

  // Delete a transaction
  delete: protectedProcedure
    .input(transactionIdSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const transaction = await Transaction.findOne({
          _id: input.id,
          userId: ctx.session.user.id,
        });

        if (!transaction) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Transaction not found",
          });
        }

        await Transaction.deleteOne({ _id: input.id });
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete transaction",
        });
      }
    }),

  // Get a single transaction by ID
  getById: protectedProcedure
    .input(transactionIdSchema)
    .query(async ({ ctx, input }) => {
      try {
        const transaction = await Transaction.findOne({
          _id: input.id,
          userId: ctx.session.user.id,
        }).populate("categoryId", "name type color icon");

        if (!transaction) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Transaction not found",
          });
        }

        return transaction;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transaction",
        });
      }
    }),

  // Get transaction summary (total income, expenses, balance)
  getSummary: protectedProcedure
    .input(getTransactionsSchema.pick({ startDate: true, endDate: true }))
    .query(async ({ ctx, input }) => {
      try {
        const filter: any = { userId: ctx.session.user.id };

        if (input.startDate || input.endDate) {
          filter.date = {};
          if (input.startDate) filter.date.$gte = input.startDate;
          if (input.endDate) filter.date.$lte = input.endDate;
        }

        const summary = await Transaction.aggregate([
          { $match: filter },
          {
            $group: {
              _id: "$type",
              total: { $sum: "$amount" },
              count: { $sum: 1 },
            },
          },
        ]);

        const income = summary.find((s) => s._id === "income")?.total || 0;
        const expenses = summary.find((s) => s._id === "expense")?.total || 0;
        const incomeCount = summary.find((s) => s._id === "income")?.count || 0;
        const expenseCount =
          summary.find((s) => s._id === "expense")?.count || 0;

        return {
          income,
          expenses,
          balance: income - expenses,
          incomeCount,
          expenseCount,
          totalTransactions: incomeCount + expenseCount,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transaction summary",
        });
      }
    }),
});
