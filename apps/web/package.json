{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port=3001", "build": "vite build", "serve": "vite preview", "start": "vite", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@tailwindcss/vite": "^4.0.15", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.5", "@tanstack/react-router": "^1.114.25", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "better-auth": "^1.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.533.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-day-picker": "^9.8.1", "react-dom": "^19.0.0", "react-hook-form": "^7.61.1", "react-resizable-panels": "^3.0.3", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^4.0.13"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-plugin": "^1.114.27", "@types/node": "^22.13.13", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "postcss": "^8.5.3", "typescript": "^5.8.3", "tailwindcss": "^4.0.15", "vite": "^6.2.2", "@tanstack/react-query-devtools": "^5.80.5"}}